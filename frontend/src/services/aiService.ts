import axios from "axios";

const API_BASE_URL =
  process.env.REACT_APP_API_BASE_URL || "http://localhost:8080";

// AI API响应接口
export interface AiApiResponse {
  response: string;
  canvasState?: any;
}

// AI聊天请求接口
export interface AiChatRequest {
  userInput: string;
  canvasState: any;
}

// AI聊天响应接口
export interface AiChatResponse {
  response: string;
  canvasState?: any;
}

// 操作类型定义
export type ActionType = "add" | "del" | "update";

// 带操作类型的元素接口
export interface ElementWithAction {
  id: string;
  action: ActionType;
  type?: string;
  properties?: any;
  placement?: any;
  timeFrame?: any;
  opacity?: number;
  trackId?: string;
  locked?: boolean;
  name?: string;
  [key: string]: any;
}

// 带操作类型的动画接口
export interface AnimationWithAction {
  id: string;
  action: ActionType;
  targetId?: string;
  duration?: number;
  type?: string;
  group?: string;
  properties?: any;
  [key: string]: any;
}

// 带操作类型的字幕接口
export interface CaptionWithAction {
  id: string;
  action: ActionType;
  startTime?: string;
  endTime?: string;
  text?: string;
  isSelected?: boolean;
  [key: string]: any;
}

// 带操作类型的轨道接口
export interface TrackWithAction {
  id: string;
  action: ActionType;
  name?: string;
  type?: string;
  elementIds?: string[];
  isVisible?: boolean;
  isLocked?: boolean;
  [key: string]: any;
}

// AI返回的canvas状态变更接口
export interface CanvasStateChanges {
  backgroundColor?: string;
  width?: number;
  height?: number;
  elements?: ElementWithAction[];
  animations?: AnimationWithAction[];
  captions?: CaptionWithAction[];
  tracks?: TrackWithAction[];
  globalCaptionStyle?: any;
  canvasScale?: number;
  canvasTranslation?: { x: number; y: number };
}

/**
 * 合并canvas状态变更
 * @param originalState 原始canvas状态
 * @param changes AI返回的状态变更
 * @returns 合并后的canvas状态
 */
export const mergeCanvasStateChanges = (
  originalState: any,
  changes: CanvasStateChanges
): any => {
  console.log("🔄 开始合并canvas状态变更");
  console.log("原始状态:", originalState);
  console.log("变更内容:", changes);

  // 深拷贝原始状态，避免修改原对象
  const mergedState = JSON.parse(JSON.stringify(originalState));

  // 确保必要的数组属性存在
  if (!mergedState.elements) {
    mergedState.elements = [];
  }
  if (!mergedState.animations) {
    mergedState.animations = [];
  }
  if (!mergedState.captions) {
    mergedState.captions = [];
  }
  if (!mergedState.tracks) {
    mergedState.tracks = [];
  }

  // 合并基础属性
  if (changes.backgroundColor !== undefined) {
    mergedState.backgroundColor = changes.backgroundColor;
  }
  if (changes.width !== undefined) {
    mergedState.width = changes.width;
  }
  if (changes.height !== undefined) {
    mergedState.height = changes.height;
  }
  if (changes.globalCaptionStyle !== undefined) {
    mergedState.globalCaptionStyle = changes.globalCaptionStyle;
  }
  if (changes.canvasScale !== undefined) {
    mergedState.canvasScale = changes.canvasScale;
  }
  if (changes.canvasTranslation !== undefined) {
    mergedState.canvasTranslation = changes.canvasTranslation;
  }

  // 合并元素变更
  if (changes.elements) {
    mergedState.elements = mergeArrayWithActions(
      mergedState.elements,
      changes.elements,
      "elements"
    );
  }

  // 合并动画变更
  if (changes.animations) {
    mergedState.animations = mergeArrayWithActions(
      mergedState.animations,
      changes.animations,
      "animations"
    );
  }

  // 合并字幕变更
  if (changes.captions) {
    mergedState.captions = mergeArrayWithActions(
      mergedState.captions,
      changes.captions,
      "captions"
    );
  }

  // 合并轨道变更
  if (changes.tracks) {
    mergedState.tracks = mergeArrayWithActions(
      mergedState.tracks,
      changes.tracks,
      "tracks"
    );
  }

  console.log("✅ canvas状态合并完成:", mergedState);
  return mergedState;
};

/**
 * 合并数组类型的状态变更
 * @param originalArray 原始数组
 * @param changesArray 变更数组
 * @param arrayType 数组类型（用于日志）
 * @returns 合并后的数组
 */
const mergeArrayWithActions = (
  originalArray: any[],
  changesArray: any[],
  arrayType: string
): any[] => {
  console.log(`🔄 处理${arrayType}变更:`, changesArray);

  // 创建原始数组的副本
  let result = [...originalArray];

  // 按操作类型分组处理
  const addItems = changesArray.filter((item) => item.action === "add");
  const updateItems = changesArray.filter((item) => item.action === "update");
  const deleteItems = changesArray.filter((item) => item.action === "del");

  // 处理删除操作
  deleteItems.forEach((item) => {
    const index = result.findIndex((existing) => existing.id === item.id);
    if (index !== -1) {
      result.splice(index, 1);
      console.log(`🗑️ 删除${arrayType}项目: ${item.id}`);
    } else {
      console.warn(`⚠️ 尝试删除不存在的${arrayType}项目: ${item.id}`);
    }
  });

  // 处理更新操作
  updateItems.forEach((item) => {
    const index = result.findIndex((existing) => existing.id === item.id);
    if (index !== -1) {
      // 移除action属性，合并其他属性
      const { action, ...updateData } = item;
      result[index] = { ...result[index], ...updateData };
      console.log(`🔄 更新${arrayType}项目: ${item.id}`);
    } else {
      console.warn(`⚠️ 尝试更新不存在的${arrayType}项目: ${item.id}`);
    }
  });

  // 处理添加操作
  addItems.forEach((item) => {
    // 检查是否已存在相同ID的项目
    const existingIndex = result.findIndex(
      (existing) => existing.id === item.id
    );
    if (existingIndex === -1) {
      // 移除action属性，添加新项目
      const { action, ...newItem } = item;
      result.push(newItem);
      console.log(`➕ 添加${arrayType}项目: ${item.id}`);
    } else {
      console.warn(`⚠️ 尝试添加已存在的${arrayType}项目: ${item.id}`);
    }
  });

  console.log(`✅ ${arrayType}合并完成，共${result.length}个项目`);
  return result;
};

/**
 * 调用AI聊天API
 * @param userInput 用户输入
 * @param canvasState 画布状态
 * @param abortController 用于取消请求的AbortController
 * @returns AI响应结果
 */
export const callAiChat = async (
  userInput: string,
  canvasState: any,
  abortController: AbortController
): Promise<AiChatResponse> => {
  try {
    const response = await axios.post<AiApiResponse>(
      `${API_BASE_URL}/api/ai/chat`,
      {
        userInput,
        canvasState,
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
        signal: abortController.signal,
      }
    );

    if (!response.data) {
      throw new Error("AI API返回空响应");
    }

    const result = response.data;
    const responseText = result.response || "抱歉，我无法处理您的请求。";
    let parsedCanvasState = null;

    // 解析canvas state
    if (result.canvasState) {
      parsedCanvasState = result.canvasState;
      console.log("后端直接返回了canvas state:", parsedCanvasState);
    } else {
      try {
        const jsonMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          parsedCanvasState = JSON.parse(jsonMatch[1]);
          console.log("从AI响应文本中解析到canvas state:", parsedCanvasState);
        } else {
          const parsed = JSON.parse(responseText);
          if (
            parsed &&
            typeof parsed === "object" &&
            (parsed.elements || parsed.width || parsed.height)
          ) {
            parsedCanvasState = parsed;
            console.log("AI响应本身就是canvas state:", parsedCanvasState);
          }
        }
      } catch (parseError) {
        console.log("AI响应不包含canvas state，作为普通文本处理");
      }
    }

    // 如果解析到了canvas state，智能处理状态合并
    if (parsedCanvasState) {
      const hasActions = checkIfHasActions(parsedCanvasState);
      const isPartialUpdate = isPartialCanvasState(parsedCanvasState);

      if (hasActions) {
        console.log("🔍 检测到canvas state包含action属性，进行增量状态合并");
        parsedCanvasState = mergeCanvasStateChanges(
          canvasState,
          parsedCanvasState
        );
      } else if (isPartialUpdate) {
        console.log("🔍 检测到部分canvas state更新，进行属性合并");
        parsedCanvasState = mergePartialCanvasState(
          canvasState,
          parsedCanvasState
        );
      } else {
        console.log("🔍 检测到完整canvas state，直接使用");
      }

      // 合并完成后进行状态验证和修复
      console.log("🔧 开始验证和修复canvas状态");
      parsedCanvasState = validateAndFixCanvasState(parsedCanvasState);
    }

    return {
      response: responseText,
      canvasState: parsedCanvasState,
    };
  } catch (error) {
    console.error("AI API调用错误:", error);

    // 如果是取消请求的错误，重新抛出
    if (error instanceof Error && error.name === "AbortError") {
      throw error;
    }

    // 如果是axios错误，提取错误信息
    if (axios.isAxiosError(error)) {
      const errorMessage =
        error.response?.data?.error || error.message || "AI API调用失败";
      throw new Error(errorMessage);
    }

    // 其他错误
    throw error;
  }
};

/**
 * 检查canvas state是否包含action属性
 * @param canvasState canvas状态对象
 * @returns 是否包含action属性
 */
const checkIfHasActions = (canvasState: any): boolean => {
  const arrayFields = ["elements", "animations", "captions", "tracks"];

  for (const field of arrayFields) {
    if (Array.isArray(canvasState[field])) {
      const hasAction = canvasState[field].some(
        (item: any) => item && typeof item === "object" && "action" in item
      );
      if (hasAction) {
        return true;
      }
    }
  }

  return false;
};

/**
 * 检查是否为部分canvas状态更新（只包含基础属性，不包含完整的元素数组）
 * @param canvasState canvas状态对象
 * @returns 是否为部分更新
 */
const isPartialCanvasState = (canvasState: any): boolean => {
  // 基础属性列表
  const basicProperties = [
    "backgroundColor",
    "width",
    "height",
    "globalCaptionStyle",
    "canvasScale",
    "canvasTranslation",
  ];

  // 数组属性列表
  const arrayProperties = ["elements", "animations", "captions", "tracks"];

  // 检查是否只包含基础属性
  const hasBasicProperties = basicProperties.some(
    (prop) => prop in canvasState
  );

  // 检查是否包含任何非空的数组属性
  const hasNonEmptyArrays = arrayProperties.some((prop) => {
    return (
      prop in canvasState &&
      Array.isArray(canvasState[prop]) &&
      canvasState[prop].length > 0
    );
  });

  // 如果包含非空数组属性，说明这是增量更新或完整更新，不是部分更新
  if (hasNonEmptyArrays) {
    console.log("🔍 检测到非空数组属性，不是部分更新");
    return false;
  }

  // 检查是否所有数组属性都缺失或为空
  const missingOrEmptyArrays = arrayProperties.every((prop) => {
    return (
      !(prop in canvasState) ||
      !Array.isArray(canvasState[prop]) ||
      canvasState[prop].length === 0
    );
  });

  // 只有当有基础属性且所有数组属性都缺失或为空时，才认为是部分更新
  const isPartial = hasBasicProperties && missingOrEmptyArrays;

  if (isPartial) {
    console.log("🔍 检测到部分状态更新：只包含基础属性，无数组内容");
  }

  return isPartial;
};

/**
 * 合并部分canvas状态（只更新基础属性，保留原有的元素、动画等）
 * @param originalState 原始canvas状态
 * @param partialState 部分状态更新
 * @returns 合并后的canvas状态
 */
const mergePartialCanvasState = (
  originalState: any,
  partialState: any
): any => {
  console.log("🔄 开始合并部分canvas状态");
  console.log("原始状态:", originalState);
  console.log("部分更新:", partialState);

  // 深拷贝原始状态，避免修改原对象
  const mergedState = JSON.parse(JSON.stringify(originalState));

  // 只更新基础属性，保留原有的数组属性
  const basicProperties = [
    "backgroundColor",
    "width",
    "height",
    "globalCaptionStyle",
    "canvasScale",
    "canvasTranslation",
  ];

  basicProperties.forEach((prop) => {
    if (prop in partialState) {
      mergedState[prop] = partialState[prop];
      console.log(`📝 更新属性 ${prop}:`, partialState[prop]);
    }
  });

  console.log("✅ 部分canvas状态合并完成:", mergedState);
  return mergedState;
};

/**
 * 验证和修复canvas状态
 * @param canvasState canvas状态对象
 * @returns 修复后的canvas状态
 */
const validateAndFixCanvasState = (canvasState: any): any => {
  console.log("🔧 开始验证和修复canvas状态");

  // 深拷贝状态，避免修改原对象
  const fixedState = JSON.parse(JSON.stringify(canvasState));

  // 确保必要的数组属性存在
  if (!fixedState.elements) fixedState.elements = [];
  if (!fixedState.tracks) fixedState.tracks = [];
  if (!fixedState.animations) fixedState.animations = [];
  if (!fixedState.captions) fixedState.captions = [];

  // 获取画布尺寸
  const canvasWidth = fixedState.width || 1920;
  const canvasHeight = fixedState.height || 1080;

  // 1. 修复元素尺寸
  fixedState.elements = fixElementSizes(
    fixedState.elements,
    canvasWidth,
    canvasHeight
  );

  // 2. 确保每个元素都有对应的轨道
  fixedState.tracks = ensureElementsHaveTracks(
    fixedState.elements,
    fixedState.tracks
  );

  // 3. 解决轨道内元素时间重叠问题
  const { elements, tracks } = resolveTimeOverlaps(
    fixedState.elements,
    fixedState.tracks
  );
  fixedState.elements = elements;
  fixedState.tracks = tracks;

  // 4. 清理无效的动画
  fixedState.animations = cleanupInvalidAnimations(
    fixedState.animations,
    fixedState.elements
  );

  console.log("✅ canvas状态验证和修复完成");
  return fixedState;
};

/**
 * 修复元素尺寸，确保不超过画布大小
 * @param elements 元素数组
 * @param canvasWidth 画布宽度
 * @param canvasHeight 画布高度
 * @returns 修复后的元素数组
 */
const fixElementSizes = (
  elements: any[],
  canvasWidth: number,
  canvasHeight: number
): any[] => {
  console.log("🔧 检查和修复元素尺寸");

  // 默认尺寸配置
  const defaultSizes = {
    text: { width: 200, height: 50 },
    image: { width: 300, height: 200 },
    video: { width: 400, height: 300 },
    audio: { width: 300, height: 60 },
    shape: { width: 100, height: 100 },
    gif: { width: 300, height: 200 },
  };

  return elements.map((element) => {
    if (!element.placement) {
      element.placement = {
        x: 0,
        y: 0,
        width: 100,
        height: 100,
        rotation: 0,
        scaleX: 1,
        scaleY: 1,
        flipX: false,
        flipY: false,
      };
    }

    const placement = element.placement;
    const elementType = element.type || "text";
    const defaultSize =
      defaultSizes[elementType as keyof typeof defaultSizes] ||
      defaultSizes.text;

    let needsFix = false;

    // 确保所有必需的 placement 属性都存在，特别是 scaleX 和 scaleY
    if (placement.rotation === undefined) {
      placement.rotation = 0;
      needsFix = true;
    }
    if (
      placement.scaleX === undefined ||
      placement.scaleX === null ||
      isNaN(placement.scaleX)
    ) {
      placement.scaleX = 1;
      needsFix = true;
    }
    if (
      placement.scaleY === undefined ||
      placement.scaleY === null ||
      isNaN(placement.scaleY)
    ) {
      placement.scaleY = 1;
      needsFix = true;
    }
    if (placement.flipX === undefined) {
      placement.flipX = false;
      needsFix = true;
    }
    if (placement.flipY === undefined) {
      placement.flipY = false;
      needsFix = true;
    }

    // 检查宽度
    if (!placement.width || placement.width > canvasWidth) {
      placement.width = Math.min(defaultSize.width, canvasWidth * 0.8);
      needsFix = true;
    }

    // 检查高度
    if (!placement.height || placement.height > canvasHeight) {
      placement.height = Math.min(defaultSize.height, canvasHeight * 0.8);
      needsFix = true;
    }

    // 检查位置，确保元素不会超出画布
    if (placement.x + placement.width > canvasWidth) {
      placement.x = Math.max(0, canvasWidth - placement.width);
      needsFix = true;
    }

    if (placement.y + placement.height > canvasHeight) {
      placement.y = Math.max(0, canvasHeight - placement.height);
      needsFix = true;
    }

    if (needsFix) {
      console.log(`🔧 修复元素 ${element.id} 的尺寸和位置:`, {
        type: elementType,
        placement: placement,
        canvas: { width: canvasWidth, height: canvasHeight },
      });
    }

    return element;
  });
};

/**
 * 确保每个元素都有对应的轨道
 * @param elements 元素数组
 * @param tracks 轨道数组
 * @returns 更新后的轨道数组
 */
const ensureElementsHaveTracks = (elements: any[], tracks: any[]): any[] => {
  console.log("🔧 检查元素轨道分配");

  const updatedTracks = [...tracks];
  const trackElementMap = new Map<string, string[]>();

  // 构建轨道-元素映射
  updatedTracks.forEach((track) => {
    trackElementMap.set(track.id, track.elementIds || []);
  });

  // 检查每个元素是否有对应的轨道
  elements.forEach((element) => {
    let hasTrack = false;

    // 检查是否已经在某个轨道中
    for (const [trackId, elementIds] of trackElementMap) {
      if (elementIds.includes(element.id)) {
        hasTrack = true;
        // 更新元素的trackId
        element.trackId = trackId;
        break;
      }
    }

    // 如果没有轨道，创建新轨道
    if (!hasTrack) {
      const newTrackId = `track-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 11)}`;
      const trackType = getTrackTypeForElement(element.type);

      const newTrack = {
        id: newTrackId,
        name: `${getTrackTypeName(trackType)} ${
          updatedTracks.filter((t) => t.type === trackType).length + 1
        }`,
        type: trackType,
        elementIds: [element.id],
        isVisible: true,
        isLocked: false,
      };

      updatedTracks.push(newTrack);
      trackElementMap.set(newTrackId, [element.id]);
      element.trackId = newTrackId;

      console.log(`🆕 为元素 ${element.id} 创建新轨道: ${newTrackId}`);
    }
  });

  return updatedTracks;
};

/**
 * 根据元素类型获取轨道类型
 * @param elementType 元素类型
 * @returns 轨道类型
 */
const getTrackTypeForElement = (elementType: string): string => {
  switch (elementType) {
    case "video":
    case "image":
    case "gif":
      return "media";
    case "audio":
      return "audio";
    case "text":
      return "text";
    case "shape":
      return "media";
    default:
      return "media";
  }
};

/**
 * 获取轨道类型的显示名称
 * @param trackType 轨道类型
 * @returns 显示名称
 */
const getTrackTypeName = (trackType: string): string => {
  switch (trackType) {
    case "media":
      return "媒体轨道";
    case "audio":
      return "音频轨道";
    case "text":
      return "文本轨道";
    default:
      return "轨道";
  }
};

/**
 * 解决轨道内元素时间重叠问题
 * @param elements 元素数组
 * @param tracks 轨道数组
 * @returns 修复后的元素和轨道数组
 */
const resolveTimeOverlaps = (
  elements: any[],
  tracks: any[]
): { elements: any[]; tracks: any[] } => {
  console.log("🔧 检查和解决时间重叠问题");

  const updatedElements = [...elements];
  const updatedTracks = [...tracks];
  const trackElementMap = new Map<string, string[]>();

  // 构建轨道-元素映射
  updatedTracks.forEach((track) => {
    trackElementMap.set(track.id, track.elementIds || []);
  });

  // 按轨道分组检查时间重叠
  for (const [trackId, elementIds] of trackElementMap) {
    const trackElements = updatedElements.filter((el) =>
      elementIds.includes(el.id)
    );

    if (trackElements.length <= 1) continue;

    // 按开始时间排序
    trackElements.sort((a, b) => {
      const startA = a.timeFrame?.start || 0;
      const startB = b.timeFrame?.start || 0;
      return startA - startB;
    });

    // 检查重叠并移动到新轨道
    for (let i = 1; i < trackElements.length; i++) {
      const currentElement = trackElements[i];
      const previousElement = trackElements[i - 1];

      if (hasTimeOverlap(previousElement, currentElement)) {
        // 创建新轨道并移动重叠元素
        const newTrackId = createNewTrackForOverlap(
          currentElement,
          trackId,
          updatedTracks
        );

        // 从原轨道移除元素
        const originalTrack = updatedTracks.find((t) => t.id === trackId);
        if (originalTrack) {
          originalTrack.elementIds = originalTrack.elementIds.filter(
            (id) => id !== currentElement.id
          );
        }

        // 更新元素的轨道ID
        currentElement.trackId = newTrackId;

        console.log(
          `⚠️ 发现时间重叠，将元素 ${currentElement.id} 移动到新轨道 ${newTrackId}`
        );
      }
    }
  }

  return { elements: updatedElements, tracks: updatedTracks };
};

/**
 * 检查两个元素是否有时间重叠
 * @param element1 元素1
 * @param element2 元素2
 * @returns 是否重叠
 */
const hasTimeOverlap = (element1: any, element2: any): boolean => {
  const start1 = element1.timeFrame?.start || 0;
  const end1 = element1.timeFrame?.end || 0;
  const start2 = element2.timeFrame?.start || 0;
  const end2 = element2.timeFrame?.end || 0;

  // 检查是否有重叠：start1 < end2 && start2 < end1
  return start1 < end2 && start2 < end1;
};

/**
 * 为重叠元素创建新轨道
 * @param element 重叠的元素
 * @param originalTrackId 原轨道ID
 * @param tracks 轨道数组
 * @returns 新轨道ID
 */
const createNewTrackForOverlap = (
  element: any,
  originalTrackId: string,
  tracks: any[]
): string => {
  const originalTrack = tracks.find((t) => t.id === originalTrackId);
  const trackType = originalTrack?.type || getTrackTypeForElement(element.type);

  const newTrackId = `track-${Date.now()}-${Math.random()
    .toString(36)
    .substring(2, 11)}`;

  const newTrack = {
    id: newTrackId,
    name: `${getTrackTypeName(trackType)} ${
      tracks.filter((t) => t.type === trackType).length + 1
    }`,
    type: trackType,
    elementIds: [element.id],
    isVisible: true,
    isLocked: false,
  };

  tracks.push(newTrack);
  return newTrackId;
};

/**
 * 清理无效的动画（targetId没有对应元素的动画）
 * @param animations 动画数组
 * @param elements 元素数组
 * @returns 清理后的动画数组
 */
const cleanupInvalidAnimations = (
  animations: any[],
  elements: any[]
): any[] => {
  console.log("🔧 检查和清理无效动画");

  if (!animations || animations.length === 0) {
    console.log("📝 没有动画需要检查");
    return animations || [];
  }

  // 创建元素ID集合，用于快速查找
  const elementIds = new Set(elements.map((element) => element.id));

  // 过滤出有效的动画
  const validAnimations = animations.filter((animation) => {
    if (!animation.targetId) {
      console.log(`⚠️ 发现没有targetId的动画: ${animation.id}`);
      return false;
    }

    if (!elementIds.has(animation.targetId)) {
      console.log(
        `🗑️ 删除无效动画 ${animation.id}，目标元素 ${animation.targetId} 不存在`
      );
      return false;
    }

    return true;
  });

  const removedCount = animations.length - validAnimations.length;
  if (removedCount > 0) {
    console.log(
      `✅ 清理完成，删除了 ${removedCount} 个无效动画，保留 ${validAnimations.length} 个有效动画`
    );
  } else {
    console.log(`✅ 所有 ${validAnimations.length} 个动画都有效，无需清理`);
  }

  return validAnimations;
};

/**
 * 创建新的AbortController用于取消请求
 * @returns AbortController实例
 */
export const createAbortController = (): AbortController => {
  return new AbortController();
};

/**
 * 检查是否为取消请求的错误
 * @param error 错误对象
 * @returns 是否为取消请求错误
 */
export const isAbortError = (error: any): boolean => {
  return error instanceof Error && error.name === "AbortError";
};
